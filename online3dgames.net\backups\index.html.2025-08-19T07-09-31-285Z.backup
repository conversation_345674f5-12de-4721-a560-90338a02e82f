<!DOCTYPE html>
<html lang="en">
<head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-WZL8VKTQ0M"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-WZL8VKTQ0M');
</script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sudoku Game - Classic Number Puzzle Challenge</title>
    <meta name="description" content="Play Sudoku online! Challenge your mind with this classic number puzzle game. Multiple difficulty levels, hints, and timer. Free Sudoku game for all skill levels.">
    <meta name="keywords" content="sudoku game, number puzzle, logic puzzle, brain game, sudoku online, puzzle game, mind game, number game">
    <meta name="author" content="Game Center">
    <meta property="og:title" content="Sudoku Game - Classic Number Puzzle">
    <meta property="og:description" content="Challenge your mind with the classic Sudoku puzzle! Fill the grid with numbers 1-9 following the rules.">
    <meta property="og:type" content="game">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Sudoku Game - Number Puzzle">
    <meta name="twitter:description" content="Test your logic skills with this classic Sudoku number puzzle game!">
    <link rel="canonical" href="/sudoku-online">
    <link rel="stylesheet" href="/sudoku-online/styles/style.css">
    <link rel="stylesheet" href="/assets/css/modal.css">
    <link rel="stylesheet" href="/assets/css/game-recommendations.css">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <button class="home-btn" onclick="window.location.href='/'">🏠 Home</button>
            <h1>🧩 Sudoku</h1>
            <div class="header-info">
                <div class="timer">⏱️ <span id="timer">00:00</span></div>
            </div>
        </div>

        <!-- Game Info -->
        <div class="game-info">
            <div class="score-panel">
                <div class="score-item">
                    <span class="label">Difficulty</span>
                    <span class="value" id="difficulty">Easy</span>
                </div>
                <div class="score-item">
                    <span class="label">Progress</span>
                    <span class="value" id="progress">0/81</span>
                </div>
                <div class="score-item">
                    <span class="label">Errors</span>
                    <span class="value" id="errors">0</span>
                </div>
            </div>
            
            <div class="controls">
                <button id="new-game-btn" class="control-btn primary">🎲 New Game</button>
                <button id="hint-btn" class="control-btn secondary">💡 Hint</button>
                <button id="solve-btn" class="control-btn success">🤖 Solve</button>
                <button id="check-btn" class="control-btn warning">✓ Check</button>
            </div>
        </div>

        <!-- Difficulty Selection -->
        <div class="difficulty-panel">
            <h3>Select Difficulty</h3>
            <div class="difficulty-buttons">
                <button class="difficulty-btn active" data-level="easy">🟢 Easy</button>
                <button class="difficulty-btn" data-level="medium">🟡 Medium</button>
                <button class="difficulty-btn" data-level="hard">🔴 Hard</button>
                <button class="difficulty-btn" data-level="expert">⚫ Expert</button>
            </div>
        </div>

        <!-- Game Container -->
        <div class="game-container">
            <!-- Sudoku Board -->
            <div class="sudoku-board" id="sudoku-board">
                <!-- 9x9 grid will be generated by JavaScript -->
            </div>

            <!-- Number Selector -->
            <div class="number-selector" id="number-selector">
                <h3>Select Number</h3>
                <div class="number-grid">
                    <button class="number-btn" data-number="1">1</button>
                    <button class="number-btn" data-number="2">2</button>
                    <button class="number-btn" data-number="3">3</button>
                    <button class="number-btn" data-number="4">4</button>
                    <button class="number-btn" data-number="5">5</button>
                    <button class="number-btn" data-number="6">6</button>
                    <button class="number-btn" data-number="7">7</button>
                    <button class="number-btn" data-number="8">8</button>
                    <button class="number-btn" data-number="9">9</button>
                    <button class="number-btn erase" data-number="0">🗑️</button>
                </div>
            </div>
        </div>

        <!-- Action Controls -->
        <div class="action-controls">
            <button id="undo-btn" class="action-btn" disabled>↶ Undo</button>
            <button id="redo-btn" class="action-btn" disabled>↷ Redo</button>
            <button id="clear-btn" class="action-btn">🗑️ Clear</button>
            <button id="save-btn" class="action-btn">💾 Save</button>
            <button id="load-btn" class="action-btn">📁 Load</button>
        </div>

        <!-- Game Start Overlay -->
        <div id="game-start" class="game-overlay">
            <div class="game-start-content">
                <h2>🧩 Sudoku</h2>
                <p>Challenge your logic skills</p>
                <p class="tip">💡 Fill the grid with numbers 1-9, so that each row, each column, and each 3x3 box does not repeat</p>
                <button id="start-btn" class="control-btn primary">Start Game</button>
            </div>
        </div>

        <!-- Difficulty Selection Modal -->
        <div id="difficulty-modal" class="game-overlay hidden">
            <div class="difficulty-modal-content">
                <div class="modal-header">
                    <h2>🎯 Select Difficulty</h2>
                    <button class="close-btn" onclick="closeDifficultyModal()">×</button>
                </div>
                <p>Select a difficulty that suits you</p>
                <div class="difficulty-options">
                    <div class="difficulty-option" data-level="easy">
                        <div class="difficulty-icon">🟢</div>
                        <h3>Easy</h3>
                        <p>35-40 empty spaces</p>
                        <p class="time-estimate">Estimated time: 5-10 minutes</p>
                        <div class="difficulty-stars">⭐⭐</div>
                    </div>
                    <div class="difficulty-option" data-level="medium">
                        <div class="difficulty-icon">🟡</div>
                        <h3>Medium</h3>
                        <p>45-50 empty spaces</p>
                        <p class="time-estimate">Estimated time: 10-20 minutes</p>
                        <div class="difficulty-stars">⭐⭐⭐</div>
                    </div>
                    <div class="difficulty-option" data-level="hard">
                        <div class="difficulty-icon">🔴</div>
                        <h3>Hard</h3>
                        <p>55-60 empty spaces</p>
                        <p class="time-estimate">Estimated time: 20-40 minutes</p>
                        <div class="difficulty-stars">⭐⭐⭐⭐</div>
                    </div>
                    <div class="difficulty-option" data-level="expert">
                        <div class="difficulty-icon">⚫</div>
                        <h3>Expert</h3>
                        <p>65-70 empty spaces</p>
                        <p class="time-estimate">Estimated time: 40 minutes or more</p>
                        <div class="difficulty-stars">⭐⭐⭐⭐⭐</div>
                    </div>
                </div>
                <div class="modal-actions">
                    <button id="cancel-difficulty-btn" class="control-btn secondary">Cancel</button>
                </div>
            </div>
        </div>

        <!-- Game Complete Overlay -->
        <div id="game-complete" class="game-overlay hidden">
            <div class="game-complete-content">
                <div class="modal-header">
                    <h2>🎉 Congratulations on completing the Sudoku!</h2>
                    <button class="close-btn" onclick="document.getElementById('game-complete').classList.add('hidden')">×</button>
                </div>
                <p>You have successfully solved the Sudoku!</p>
                <div class="completion-stats">
                    <div class="stat-item">
                        <span class="stat-label">Time</span>
                        <span class="stat-value" id="final-time">00:00</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Errors</span>
                        <span class="stat-value" id="final-errors">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Difficulty</span>
                        <span class="stat-value" id="final-difficulty">Easy</span>
                    </div>
                </div>
                <div class="completion-actions">
                    <button id="new-game-complete-btn" class="control-btn primary">Play Again</button>
                    <button id="change-difficulty-complete-btn" class="control-btn secondary">Select Difficulty</button>
                    <button id="share-btn" class="control-btn">Share Score</button>
                </div>
            </div>
        </div>

        <!-- Game Failed Overlay -->
        <div id="game-failed" class="game-overlay hidden">
            <div class="game-failed-content">
                <div class="modal-header">
                    <h2>😔 Game cannot continue</h2>
                    <button class="close-btn" onclick="closeFailedDialog()">×</button>
                </div>
                <p>The current state is unsolvable, and cannot continue</p>
                <div class="failed-tips">
                    <div class="tip-item">
                        <span class="tip-icon">💡</span>
                        <span class="tip-text">Check the red marked cells</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">↶</span>
                        <span class="tip-text">Use the undo function to return to the previous state</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">🎲</span>
                        <span class="tip-text">Or start a new game</span>
                    </div>
                </div>
                <div class="failed-actions">
                    <button id="retry-btn" class="control-btn primary">Try Again</button>
                    <button id="change-difficulty-btn" class="control-btn secondary">Select Difficulty</button>
                    <button id="close-failed-btn" class="control-btn">Continue</button>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="instructions">
            <h3>🎯 Game Rules</h3>
            <div class="instruction-grid">
                <div class="instruction-item">
                    <div class="instruction-icon">📋</div>
                    <div class="instruction-text">
                        <h4>Basic Rules</h4>
                        <p>Fill the grid with numbers 1-9, so that each row, each column, and each 3x3 box does not repeat</p>
                    </div>
                </div>
                <div class="instruction-item">
                    <div class="instruction-icon">🎯</div>
                    <div class="instruction-text">
                        <h4>Operation Method</h4>
                        <p>Click the empty space to select the position, then click the number button to fill in the number</p>
                    </div>
                </div>
                <div class="instruction-item">
                    <div class="instruction-icon">💡</div>
                    <div class="instruction-text">
                        <h4>Hint Function</h4>
                        <p>When you encounter difficulties, you can use the hint function, or let AI automatically solve</p>
                    </div>
                </div>
                <div class="instruction-item">
                    <div class="instruction-icon">⚡</div>
                    <div class="instruction-text">
                        <h4>Quick Operation</h4>
                        <p>Support keyboard input, undo redo, save progress, etc.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Content Section -->
    <div class="seo-content-section" style="max-width: 1200px; margin: 40px auto; padding: 20px; font-family: Arial, sans-serif; line-height: 1.6; position: relative; z-index: 1; border-radius: 10px;">
        <h2 style="color: #ffffff; margin-bottom: 20px; font-size: 28px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">Sudoku Game - Master the Ultimate Number Puzzle</h2>

        <div style="display: grid;  gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🧩 Logic Puzzle Excellence</h3>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Challenge your mind with the world's most popular number puzzle. Fill the 9x9 grid so that every row, column, and 3x3 box contains all digits from 1 to 9 exactly once.</p>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Features multiple difficulty levels, intelligent hint system, and error checking to help you improve your logical reasoning and problem-solving skills.</p>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🎯 Game Features</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li><strong style="color: #ffffff;">Multiple Levels:</strong> Easy, Medium, Hard, Expert</li>
                    <li><strong style="color: #ffffff;">Smart Hints:</strong> Intelligent solving assistance</li>
                    <li><strong style="color: #ffffff;">Error Detection:</strong> Real-time mistake highlighting</li>
                    <li><strong style="color: #ffffff;">Note Taking:</strong> Pencil marks for strategy</li>
                    <li><strong style="color: #ffffff;">Timer & Stats:</strong> Track your progress</li>
                </ul>
            </div>
        </div>

        <div style="margin-bottom: 30px;">
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🧠 Brain Training Benefits</h3>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Regular Sudoku practice has been scientifically proven to improve memory, concentration, and logical thinking. Our game provides the perfect platform for daily brain exercise.</p>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Start with easier puzzles and gradually work your way up to expert level as you develop advanced solving techniques and pattern recognition skills.</p>
        </div>

        <div>
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🏆 Become a Sudoku Master</h3>
            <p style="margin-bottom: 10px; color: #f0f0f0;">Train your brain with our comprehensive Sudoku game featuring unlimited puzzles and advanced solving tools. Perfect for beginners and experts alike.</p>
            <p style="color: #f0f0f0;">Challenge yourself daily and watch your logical reasoning skills improve with every puzzle you solve!</p>
        </div>
    </div>

                                                                                            <!-- Game Recommendations -->
    <div class="game-recommendations similar-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">More Puzzle Games</h3>
            <p class="recommendations-subtitle">Continue exploring puzzle games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/2048" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🔢</div>
                        <h4 class="game-name">2048</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/tetris-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🟦</div>
                        <h4 class="game-name">Tetris</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/memoryGame" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧠</div>
                        <h4 class="game-name">Memory Game</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <div class="game-recommendations other-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">Other Games</h3>
            <p class="recommendations-subtitle">Discover more amazing games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎰</div>
                        <h4 class="game-name">Blackjack</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/blackjack-online" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎯</div>
                        <h4 class="game-name">Blackjack Practice</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/free-bet-blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🆓</div>
                        <h4 class="game-name">Free Bet Blackjack</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/pontoon-card-game" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🚤</div>
                        <h4 class="game-name">Pontoon</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/hearts" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♥️</div>
                        <h4 class="game-name">Hearts</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/klondike-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">Solitaire</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/spider-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🕷️</div>
                        <h4 class="game-name">Spider Solitaire</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/texas-holdem-game" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🎲</div>
                        <h4 class="game-name">Texas Hold'em</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/spades" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♠️</div>
                        <h4 class="game-name">Spades</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row">
                <a href="/freecell-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">FreeCell Solitaire</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/mahjong-solitaire" class="recommendation-card" data-category="board-games">
                    <div class="card-image">
                        <div class="game-icon">🀄</div>
                        <h4 class="game-name">Mahjong Solitaire</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/chess" class="recommendation-card" data-category="board-games">
                    <div class="card-image">
                        <div class="game-icon">♔</div>
                        <h4 class="game-name">Chess</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/snake-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🐍</div>
                        <h4 class="game-name">Snake</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/breakout-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🧱</div>
                        <h4 class="game-name">Breakout</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/reaction-test" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">⚡</div>
                        <h4 class="game-name">Reaction Test</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/pop-it-game" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🫧</div>
                        <h4 class="game-name">Pop-it</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/fidget-spinner" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌀</div>
                        <h4 class="game-name">Fidget Spinner</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
                <a href="/breathing-ball" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌬️</div>
                        <h4 class="game-name">Breathing Ball</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/drawing-wall" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🎨</div>
                        <h4 class="game-name">Drawing Wall</h4>
                        <div class="game-rating">⭐ 4.2</div>
                    </div>
                </a>
                <a href="/bubble-float" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">💭</div>
                        <h4 class="game-name">Bubble Float</h4>
                        <div class="game-rating">⭐ 4.1</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <script src="/assets/js/modal.js"></script>
    <script src="/sudoku-online/src/sudoku-solver.js"></script>
    <script src="/sudoku-online/src/sudoku-generator.js"></script>
    <script src="/sudoku-online/src/sudoku.js"></script>
    <script src="/sudoku-online/src/game.js"></script>
    <script src="/assets/js/game-recommendations.js"></script>
</body>
</html>