<!DOCTYPE html>
<html lang="en">
<head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-WZL8VKTQ0M"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-WZL8VKTQ0M');
</script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Spider Solitaire - Classic Card Game Online Free</title>
    <meta name="description" content="Play the classic Spider Solitaire card game online for free. Three difficulty levels, smooth animations, and intuitive gameplay. Master the ultimate solitaire challenge!">
    <meta name="keywords" content="spider solitaire, solitaire card game, online solitaire, free card games, spider patience, classic solitaire, card game strategy">

    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <meta name="theme-color" content="#2d5016">
    <meta name="msapplication-TileColor" content="#2d5016">
    <meta name="msapplication-navbutton-color" content="#2d5016">
    <meta name="screen-orientation" content="landscape">
    <meta name="orientation" content="landscape">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/spider-solitaire/css/styles.css">
    <link rel="stylesheet" href="/spider-solitaire/css/spider.css">
    <link rel="stylesheet" href="/assets/css/game-recommendations.css">

    <!-- jQuery -->
    <script src="/assets/js/jquery-3.7.1.min.js"></script>
</head>
<body>
    <!-- SEO H1 for search engines (hidden from users) -->
    <h1 style="position: absolute; left: -9999px; top: -9999px; visibility: hidden;">Spider Solitaire - Classic Card Game Online Free</h1>

    <div id="landscape-prompt" class="landscape-prompt">
        <div class="landscape-content">
            <div class="rotate-icon">📱</div>
            <h2>Better Experience in Landscape</h2>
            <p>Please rotate your device to landscape mode for the best Spider Solitaire gaming experience</p>
        </div>
    </div>

    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <h2>Spider Solitaire</h2>
                <div class="game-stats">
                    <div class="stat">
                        <span class="stat-label">Score:</span>
                        <span id="score">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Time:</span>
                        <span id="timer">00:00</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Moves:</span>
                        <span id="moves">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Complete:</span>
                        <span id="completed">0/8</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="difficulty-buttons">
                    <button id="easyBtn" class="btn btn-difficulty active">Easy</button>
                    <button id="mediumBtn" class="btn btn-difficulty">Medium</button>
                    <button id="hardBtn" class="btn btn-difficulty">Hard</button>
                </div>
                <button id="homeBtn" class="btn btn-secondary">🏠 Home</button>
                <button id="newGameBtn" class="btn btn-primary">New Game</button>
                <button id="undoBtn" class="btn btn-secondary">Undo</button>
                <button id="hintBtn" class="btn btn-secondary">Hint</button>
                <button id="helpBtn" class="btn btn-secondary">Help</button>
                <button id="fullscreenBtn" class="btn btn-secondary">⛶</button>
            </div>
        </header>

        <!-- Mobile/Tablet Stock Button (Absolute positioned) -->
        <button id="stockBtn" class="btn-stock-floating" title="Click to Deal 10 Cards">
            <span class="stock-icon">🂠</span>
            <span id="stockCount" class="stock-count">5</span>
        </button>

        <!-- Game Board -->
        <main class="game-board spider-board">
            <!-- Completed Sequences Area (Top) -->
            <div class="completed-area">
                <div class="completed-sequences" id="completedSequences">
                    <div class="completed-placeholder">Completed sequences will appear here</div>
                </div>
            </div>

            <!-- Tableau Piles (10 columns for Spider) -->
            <div class="tableau-area spider-tableau">
                <div class="tableau-pile" id="tableau-0"></div>
                <div class="tableau-pile" id="tableau-1"></div>
                <div class="tableau-pile" id="tableau-2"></div>
                <div class="tableau-pile" id="tableau-3"></div>
                <div class="tableau-pile" id="tableau-4"></div>
                <div class="tableau-pile" id="tableau-5"></div>
                <div class="tableau-pile" id="tableau-6"></div>
                <div class="tableau-pile" id="tableau-7"></div>
                <div class="tableau-pile" id="tableau-8"></div>
                <div class="tableau-pile" id="tableau-9"></div>
            </div>

            <!-- Hidden Stock Pile for Desktop -->
            <div class="stock-area desktop-only">
                <div class="stock-pile" id="stock">
                    <div class="card-back"></div>
                </div>
                <div class="stock-info">
                    <p>Click to Deal</p>
                    <small>Deals 10 cards</small>
                </div>
            </div>
        </main>

        <!-- Game Messages -->
        <div id="gameMessage" class="game-message hidden">
            <div class="message-content">
                <h2 id="messageTitle">Congratulations!</h2>
                <p id="messageText">You won the game!</p>
                <div class="message-stats">
                    <div>Score: <span id="finalScore">0</span></div>
                    <div>Time: <span id="finalTime">00:00</span></div>
                    <div>Moves: <span id="finalMoves">0</span></div>
                </div>
                <div class="message-buttons">
                    <button id="playAgainBtn" class="btn btn-primary">Play Again</button>
                    <button id="closeMessageBtn" class="btn btn-secondary">Close</button>
                </div>
            </div>
        </div>

        <!-- Help Panel -->
        <div id="helpPanel" class="help-panel hidden">
            <div class="help-content">
                <div class="help-header">
                    <h3>🕷️ Spider Solitaire</h3>
                    <button id="closeHelpBtn" class="close-btn">×</button>
                </div>

                <div class="help-body">
                    <div class="help-section">
                        <h4>🎯 Game Objective</h4>
                        <p>Arrange all 104 cards by suit from King to Ace in descending order to complete 8 full sequences.</p>
                    </div>

                    <div class="help-section">
                        <h4>📋 Game Rules</h4>
                        <ul>
                            <li><strong>Movement Rules:</strong> Cards can be placed in descending order (any suit)</li>
                            <li><strong>Multi-card Movement:</strong> Only same-suit consecutive descending sequences can be moved together</li>
                            <li><strong>Complete Sequences:</strong> Same-suit K to A sequences are automatically removed</li>
                            <li><strong>Dealing:</strong> Click stock to deal one card to each column (all columns must have cards)</li>
                            <li><strong>Flipping Cards:</strong> Face-down cards are automatically flipped when exposed</li>
                        </ul>
                    </div>

                    <div class="help-section">
                        <h4>🎮 Difficulty Levels</h4>
                        <ul>
                            <li><strong>Easy:</strong> Uses only Spades (one suit)</li>
                            <li><strong>Medium:</strong> Uses Spades and Hearts (two suits)</li>
                            <li><strong>Hard:</strong> Uses all four suits</li>
                        </ul>
                    </div>

                    <div class="help-section">
                        <h4>⚡ Control Tips</h4>
                        <ul>
                            <li><strong>Drag & Drop:</strong> Drag cards or sequences to target positions</li>
                            <li><strong>Double Click:</strong> Double-click cards to auto-move to suitable positions</li>
                            <li><strong>Multi-select:</strong> Click any card in a sequence to select it and all consecutive same-suit cards below</li>
                        </ul>
                    </div>

                    <div class="help-section">
                        <h4>🏆 Scoring System</h4>
                        <ul>
                            <li>Flip face-down card: <strong>+5 points</strong></li>
                            <li>Complete a sequence: <strong>+100 points</strong></li>
                            <li>Move cards: <strong>+1 point</strong></li>
                        </ul>
                    </div>
                </div>

                <div class="help-footer">
                    <button id="closeHelpBtnBottom" class="btn btn-primary">Start Playing!</button>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Content Section -->
    <div class="seo-content-section" style="max-width: 1200px; margin: 40px auto; padding: 20px; font-family: Arial, sans-serif; line-height: 1.6; position: relative; z-index: 1; border-radius: 10px;">
        <h2 style="color: #ffffff; margin-bottom: 20px; font-size: 28px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">Spider Solitaire - The Ultimate Card Game Challenge</h2>

        <div style="display: grid;  gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🕷️ Classic Spider Solitaire Experience</h3>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Experience the most challenging and rewarding solitaire variant ever created. Spider Solitaire demands strategic thinking, patience, and skill as you work to arrange 104 cards into eight complete sequences from King to Ace.</p>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Our implementation features smooth card animations, intuitive drag-and-drop controls, and three difficulty levels that cater to both beginners and solitaire masters.</p>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🎯 Strategic Gameplay Features</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li><strong style="color: #ffffff;">Three Difficulty Modes:</strong> Easy (1 suit), Medium (2 suits), Hard (4 suits)</li>
                    <li><strong style="color: #ffffff;">Smart Auto-Move:</strong> Double-click for intelligent card placement</li>
                    <li><strong style="color: #ffffff;">Undo System:</strong> Reverse moves to perfect your strategy</li>
                    <li><strong style="color: #ffffff;">Hint System:</strong> Get suggestions when you're stuck</li>
                    <li><strong style="color: #ffffff;">Score Tracking:</strong> Monitor your progress and improvement</li>
                </ul>
            </div>
        </div>

        <div style="margin-bottom: 30px;">
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🏆 Master the Spider Solitaire Challenge</h3>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Spider Solitaire is considered one of the most difficult solitaire games, requiring both tactical planning and strategic foresight. Unlike other solitaire variants, you must build sequences within the tableau itself, making every move crucial to your success.</p>
            <p style="margin-bottom: 15px; color: #f0f0f0;">The game rewards patience and careful planning. Each completed sequence of 13 cards (King through Ace) in the same suit is automatically removed, bringing you closer to victory. With 104 cards and only 10 tableau columns, space management becomes a critical skill.</p>
        </div>

        <div style="display: grid;  gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🎮 Perfect for All Skill Levels</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li>Beginners: Start with Easy mode (single suit)</li>
                    <li>Intermediate: Challenge yourself with Medium (two suits)</li>
                    <li>Experts: Master the ultimate Hard mode (four suits)</li>
                    <li>Mobile-friendly: Play anywhere, anytime</li>
                </ul>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">⚡ Advanced Features</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li>Responsive design for all devices</li>
                    <li>Fullscreen mode for immersive play</li>
                    <li>Timer and move counter</li>
                    <li>Smooth animations and transitions</li>
                </ul>
            </div>
        </div>

        <div>
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🌟 Why Choose Our Spider Solitaire?</h3>
            <p style="margin-bottom: 10px; color: #f0f0f0;">Our Spider Solitaire game combines classic gameplay with modern technology. Enjoy crisp graphics, smooth animations, and intelligent game mechanics that make every session engaging and challenging.</p>
            <p style="color: #f0f0f0;">Whether you're a casual player looking for relaxation or a serious strategist seeking the ultimate solitaire challenge, our Spider Solitaire delivers an exceptional gaming experience that keeps you coming back for more!</p>
        </div>
    </div>

                                                                                                <!-- Game Recommendations -->
    <div class="game-recommendations similar-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">More Card Games</h3>
            <p class="recommendations-subtitle">Continue exploring card games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/hearts" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♥️</div>
                        <h4 class="game-name">Hearts</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/klondike-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">Solitaire</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/texas-holdem-game" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🎲</div>
                        <h4 class="game-name">Texas Hold'em</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/spades" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♠️</div>
                        <h4 class="game-name">Spades</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <div class="game-recommendations other-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">Other Games</h3>
            <p class="recommendations-subtitle">Discover more amazing games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/2048" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🔢</div>
                        <h4 class="game-name">2048</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎰</div>
                        <h4 class="game-name">Blackjack</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/blackjack-online" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎯</div>
                        <h4 class="game-name">Blackjack Practice</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/free-bet-blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🆓</div>
                        <h4 class="game-name">Free Bet Blackjack</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/pontoon-card-game" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🚤</div>
                        <h4 class="game-name">Pontoon</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/mahjong-solitaire" class="recommendation-card" data-category="board-games">
                    <div class="card-image">
                        <div class="game-icon">🀄</div>
                        <h4 class="game-name">Mahjong Solitaire</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/chess" class="recommendation-card" data-category="board-games">
                    <div class="card-image">
                        <div class="game-icon">♔</div>
                        <h4 class="game-name">Chess</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/sudoku-online" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧩</div>
                        <h4 class="game-name">Sudoku</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/tetris-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🟦</div>
                        <h4 class="game-name">Tetris</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row">
                <a href="/memoryGame" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧠</div>
                        <h4 class="game-name">Memory Game</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/snake-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🐍</div>
                        <h4 class="game-name">Snake</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/breakout-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🧱</div>
                        <h4 class="game-name">Breakout</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/reaction-test" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">⚡</div>
                        <h4 class="game-name">Reaction Test</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/pop-it-game" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🫧</div>
                        <h4 class="game-name">Pop-it</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/fidget-spinner" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌀</div>
                        <h4 class="game-name">Fidget Spinner</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
                <a href="/breathing-ball" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌬️</div>
                        <h4 class="game-name">Breathing Ball</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/drawing-wall" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🎨</div>
                        <h4 class="game-name">Drawing Wall</h4>
                        <div class="game-rating">⭐ 4.2</div>
                    </div>
                </a>
                <a href="/bubble-float" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">💭</div>
                        <h4 class="game-name">Bubble Float</h4>
                        <div class="game-rating">⭐ 4.1</div>
                    </div>
                </a>
                <a href="/particle-trail" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">✨</div>
                        <h4 class="game-name">Particle Trail</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
                <a href="/typing-machine" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">⌨️</div>
                        <h4 class="game-name">Typing Machine</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <script src="/assets/js/modal.js"></script>
    <script src="/spider-solitaire/js/spider.js"></script>
    <script src="/assets/js/game-recommendations.js"></script>
</body>
</html>